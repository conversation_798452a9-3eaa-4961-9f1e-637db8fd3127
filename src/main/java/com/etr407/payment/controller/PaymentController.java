package com.etr407.payment.controller;

import com.etr407.observability.metrics.MetricsRepository;
import com.etr407.payment.PaymentApplicationException;
import com.etr407.payment.PaymentApplicationReturnCode;
import com.etr407.payment.config.OpenAPICommonHeaders;
import com.etr407.payment.metrics.OnlinePaymentTags;
import com.etr407.payment.model.*;
import com.etr407.payment.service.GiftCardService;
import com.etr407.payment.service.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nonnull;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
@OpenAPICommonHeaders
public class PaymentController {

    private final PaymentService paymentService;
    private final GiftCardService giftCardService;
    private final MetricsRepository metricsRepository;


    @Operation(description = "Submit a request to process a payment (i.e. request the creation of a payment-request resource)")
    @PostMapping("/payment-request")
    @PreAuthorize("isAuthenticated() or @recaptchaService.verify(#recaptcha)")
    public ResponseEntity<PaymentReceipt> makePayment(
            @Valid @RequestBody final PaymentRequest request,
            @RequestHeader final Channel channel,
            @RequestHeader(value = "x-recaptcha", required = false) final String recaptcha,
            @RequestHeader(value = "tokenizationStatus", required = false) final TokenizationStatus tokenizationStatus) {

        log.info("makePayment() >>> [{}], [{}], [{}], [{}], [{}]",
                request.accountNumber(), request.paymentMethod(), request.amount(), channel, tokenizationStatus);

        ResponseEntity<PaymentReceipt> response;

        try {
            response = processPaymentRequest(request, channel, tokenizationStatus);
        } catch (Exception e) {
            metricsRepository.getCounter("online.payment.count",
                    new OnlinePaymentTags(request.paymentMethod(), "FAIL")).increment();
            throw e;
        }

        recordPaymentMetrics(request, response.getBody());
        return response;
    }

    private @Nonnull ResponseEntity<PaymentReceipt> processPaymentRequest(PaymentRequest request, Channel channel, TokenizationStatus tokenizationStatus) {
        if (PaymentMethod.CREDIT_CARD == request.paymentMethod()) {
            return handleCreditCardPayment(request, channel, tokenizationStatus);
        }

        if (PaymentMethod.GIFT_CARD == request.paymentMethod()) {
            return handleGiftCardPayment(request, channel);
        }

        throw new PaymentApplicationException("payment method not supported",
                PaymentApplicationReturnCode.ERROR_SYSTEM_FAILURE.code(),
                PaymentApplicationReturnCode.ERROR_SYSTEM_FAILURE.defaultDescription() +
                        " payment method not supported.");
    }

    @Operation(description = "Submit a request to get gift card balance")
    @PostMapping("/giftcard-balance")
    public ResponseEntity<GiftCardBalanceReceipt> getBalance(@Valid @RequestBody final GiftCardBalanceRequest request) {
        return ResponseEntity.ok(giftCardService.getBalance(request));
    }

    private @Nonnull ResponseEntity<PaymentReceipt> handleCreditCardPayment(
            PaymentRequest request, Channel channel, TokenizationStatus tokenizationStatus) {

        if (tokenizationStatus == null) {
            throw new PaymentApplicationException("card number tokenization status missing",
                    PaymentApplicationReturnCode.ERROR_SYSTEM_FAILURE.code(),
                    PaymentApplicationReturnCode.ERROR_SYSTEM_FAILURE.defaultDescription() +
                            " card number tokenization missing.");
        }

        if (TokenizationStatus.ok != tokenizationStatus) {
            throw new PaymentApplicationException("card number tokenization failed",
                    PaymentApplicationReturnCode.ERROR_SYSTEM_FAILURE.code(),
                    PaymentApplicationReturnCode.ERROR_SYSTEM_FAILURE.defaultDescription() +
                            " card number tokenization failed.");
        }

        return ResponseEntity.ok(paymentService.makePayment(request, new PaymentRequestContext(channel)));
    }

    private @Nonnull ResponseEntity<PaymentReceipt> handleGiftCardPayment(PaymentRequest request, Channel channel) {
        return ResponseEntity.ok(paymentService.makePayment(request, new PaymentRequestContext(channel)));
    }

    private void recordPaymentMetrics(PaymentRequest request, PaymentReceipt receipt) {
        String status = String.valueOf(receipt.result());
        OnlinePaymentTags tags = new OnlinePaymentTags(request.paymentMethod(), status);

        metricsRepository.getCounter("online.payment.count", tags)
                .increment();

        metricsRepository.getDistributionSummary("online.payment.amount", tags)
                .record(request.amount().doubleValue());
    }
}
